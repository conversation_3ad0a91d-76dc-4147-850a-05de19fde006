'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';

const MAX_RETRIES = 10; // Increased to 10 for a 10-second window
const RETRY_DELAY_MS = 1000;

export default function SetupPage() {
  const router = useRouter();
  const [message, setMessage] = useState('Finalizing your account setup...');
  const [error, setError] = useState('');

  useEffect(() => {
    const token = localStorage.getItem('pairsona_token');

    if (!token) {
      // This shouldn't happen if OTP verification worked, but as a safeguard:
      router.replace('/login');
      return;
    }

    let attempt = 0;

    const pollProfile = async () => {
      attempt++;

      try {
        const response = await fetch('https://api.pairsona.id/auth/profile', {
          headers: { Authorization: `Bearer ${token}` },
          cache: 'no-store',
        });

        if (response.ok) {
          // SUCCESS! Profile is ready.
          const profile = await response.json();
          const destination = profile.psychTestCompleted ? '/dashboard' : '/personality-test';
          router.replace(destination);
          return; // Stop polling
        }

        // If not OK, check if we should retry.
        if (attempt < MAX_RETRIES) {
          setMessage(`Verifying your session... (Attempt ${attempt}/${MAX_RETRIES})`);
          setTimeout(pollProfile, RETRY_DELAY_MS);
        } else {
          // Max retries reached, this is a persistent failure.
          setError('Could not verify your session after several attempts. Please try logging in.');
          // Clear tokens and redirect
          localStorage.removeItem('pairsona_token');
          Cookies.remove('pairsona_token');
          setTimeout(() => router.replace('/login'), 4000);
        }
      } catch (err) {
        // Network error or other unexpected fetch failure.
        setError('A network error occurred. Please check your connection and try again.');
        // Clear tokens and redirect
        localStorage.removeItem('pairsona_token');
        Cookies.remove('pairsona_token');
        setTimeout(() => router.replace('/login'), 4000);
      }
    };

    pollProfile();
  }, [router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-eggshell">
      <div className="text-center p-8">
        {!error && (
          <>
            <div className="w-16 h-16 border-4 border-dashed rounded-full animate-spin border-accent mx-auto"></div>
            <h1 className="mt-8 text-2xl font-semibold text-gray-700">Just a moment</h1>
            <p className="mt-2 text-gray-500">{message}</p>
          </>
        )}
        {error && (
          <>
            <h1 className="text-2xl font-semibold text-accent">Setup Failed</h1>
            <p className="mt-2 text-gray-600">{error}</p>
            <p className="mt-1 text-sm text-gray-500">You will be redirected to the login page.</p>
          </>
        )}
      </div>
    </div>
  );
}
