"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { User, Edit, Save, Upload, Camera, X, Plus } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";

interface Photo {
  id: string;
  url: string;
  key: string;
}

interface Profile {
  id: string;
  name: string;
  email: string;
  phoneNumber?: string;
  image?: string;
  dateOfBirth?: string;
  photos: Photo[];
  gender?: string;
  religion?: string;
  occupation?: string;
  isSmoker: boolean;
  acceptDifferentReligion: boolean;
  about?: string;
  psychTestCompleted: boolean;
  status: string;
}

export default function ProfilePage() {
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [uploadingPhotos, setUploadingPhotos] = useState(false);
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const photosInputRef = useRef<HTMLInputElement>(null);
  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('pairsona_token');
          router.replace('/login');
          return;
        }
        throw new Error('Failed to fetch profile');
      }

      const data = await response.json();
      console.log('Profile data from API:', data); // Debug log
      console.log('Photos array:', data.photos); // Debug photos specifically
      setProfile(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!profile) return;

    try {
      setSaving(true);
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const updateData = {
        name: profile.name,
        gender: profile.gender,
        religion: profile.religion,
        occupation: profile.occupation,
        isSmoker: profile.isSmoker,
        acceptDifferentReligion: profile.acceptDifferentReligion,
        about: profile.about,
        dateOfBirth: profile.dateOfBirth,
      };

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/profile`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error('Failed to update profile');
      }

      const updatedProfile = await response.json();
      setProfile(updatedProfile);
      setEditing(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save profile');
    } finally {
      setSaving(false);
    }
  };

  const handleImageUpload = async (file: File) => {
    try {
      setUploadingImage(true);
      const token = localStorage.getItem('pairsona_token');
      if (!token) return;

      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/profile/image`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const result = await response.json();
      if (profile) {
        setProfile({ ...profile, image: result.url });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload image');
    } finally {
      setUploadingImage(false);
    }
  };

  const handlePhotosUpload = async (files: FileList) => {
    try {
      setUploadingPhotos(true);
      const token = localStorage.getItem('pairsona_token');
      if (!token) return;

      const formData = new FormData();
      Array.from(files).forEach(file => {
        formData.append('files', file);
      });

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/profile/photos`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload photos');
      }

      const newPhotos = await response.json();
      if (profile) {
        setProfile({
          ...profile,
          photos: [...profile.photos, ...newPhotos.map((photo: any, index: number) => ({
            id: `new-${Date.now()}-${index}`,
            url: photo.url,
            key: photo.key
          }))]
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload photos');
    } finally {
      setUploadingPhotos(false);
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getReligionIcon = (religion: string) => {
    const religionMap: { [key: string]: string } = {
      'islam': '☪️',
      'kristen': '✝️',
      'katolik': '✝️',
      'hindu': '🕉️',
      'buddha': '☸️',
      'konghucu': '☯️'
    };
    return religionMap[religion?.toLowerCase()] || '🙏';
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Profile</h1>
          <p className="text-muted-foreground">Loading your profile...</p>
        </div>
        <div className="grid gap-6 md:grid-cols-3">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !profile) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Profile</h1>
          <p className="text-muted-foreground">Manage your personal information and preferences.</p>
        </div>
        <Card className="p-6">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error || 'Failed to load profile'}</p>
            <Button onClick={fetchProfile}>Try Again</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <X className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
              <div className="ml-auto pl-3">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setError(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Profile</h1>
          <p className="text-muted-foreground">Manage your personal information and preferences.</p>
        </div>
        <div className="flex gap-2">
          {editing && (
            <Button
              variant="outline"
              onClick={() => {
                setEditing(false);
                fetchProfile(); // Reset changes
              }}
            >
              Cancel
            </Button>
          )}
          <Button
            className={editing ? "bg-green-600 hover:bg-green-700" : "bg-[#D0544D] hover:bg-[#D0544D]/90"}
            onClick={editing ? handleSave : () => setEditing(true)}
            disabled={saving}
          >
            {editing ? (
              <>
                <Save className="mr-2 h-4 w-4" />
                {saving ? 'Saving...' : 'Save Changes'}
              </>
            ) : (
              <>
                <Edit className="mr-2 h-4 w-4" />
                Edit Profile
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Profile Summary */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>Profile Summary</CardTitle>
            <CardDescription>Your basic information</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center text-center">
            <div className="relative mb-4">
              <Avatar className="w-32 h-32">
                <AvatarImage src={profile.image} alt={profile.name} />
                <AvatarFallback className="bg-[#D0544D]/20 text-[#D0544D] text-2xl font-bold">
                  {getInitials(profile.name)}
                </AvatarFallback>
              </Avatar>
              <Button
                size="sm"
                className="absolute bottom-0 right-0 rounded-full w-8 h-8 p-0"
                onClick={() => fileInputRef.current?.click()}
                disabled={uploadingImage}
              >
                {uploadingImage ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Camera className="w-4 h-4" />
                )}
              </Button>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                className="hidden"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleImageUpload(file);
                }}
              />
            </div>
            <h3 className="text-xl font-bold">{profile.name}</h3>
            <p className="text-sm text-muted-foreground">{profile.email}</p>
            {profile.dateOfBirth && (
              <p className="text-sm text-muted-foreground">
                {calculateAge(profile.dateOfBirth)} tahun
              </p>
            )}
            <div className="flex items-center gap-2 mt-2">
              {profile.religion && (
                <Badge variant="secondary" className="text-xs">
                  {getReligionIcon(profile.religion)} {profile.religion}
                </Badge>
              )}
              {profile.occupation && (
                <Badge variant="outline" className="text-xs">
                  {profile.occupation}
                </Badge>
              )}
            </div>
            <div className="mt-4 w-full">
              <div className="flex justify-between text-sm mb-1">
                <span>Profile Status</span>
                <Badge variant={profile.psychTestCompleted ? "default" : "secondary"}>
                  {profile.psychTestCompleted ? "Complete" : "Incomplete"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Personal Information */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
            <CardDescription>Update your personal details</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Full Name</label>
                  <Input
                    disabled={!editing}
                    value={profile.name}
                    onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Email</label>
                  <Input
                    type="email"
                    disabled={true} // Email should not be editable
                    value={profile.email}
                    className="bg-gray-50"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Phone Number</label>
                  <Input
                    type="tel"
                    disabled={true} // Phone should not be editable via this form
                    value={profile.phoneNumber || 'Not provided'}
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Date of Birth</label>
                  <Input
                    type="date"
                    disabled={!editing}
                    value={profile.dateOfBirth ? new Date(profile.dateOfBirth).toISOString().split('T')[0] : ''}
                    onChange={(e) => setProfile({ ...profile, dateOfBirth: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Gender</label>
                  <Select
                    disabled={!editing}
                    value={profile.gender || ''}
                    onValueChange={(value) => setProfile({ ...profile, gender: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Religion</label>
                  <Select
                    disabled={!editing}
                    value={profile.religion || ''}
                    onValueChange={(value) => setProfile({ ...profile, religion: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select religion" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="islam">Islam</SelectItem>
                      <SelectItem value="kristen">Kristen</SelectItem>
                      <SelectItem value="katolik">Katolik</SelectItem>
                      <SelectItem value="hindu">Hindu</SelectItem>
                      <SelectItem value="buddha">Buddha</SelectItem>
                      <SelectItem value="konghucu">Konghucu</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Occupation</label>
                <Input
                  disabled={!editing}
                  value={profile.occupation || ''}
                  onChange={(e) => setProfile({ ...profile, occupation: e.target.value })}
                  placeholder="Your occupation"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Smoking Status</label>
                  <Select
                    disabled={!editing}
                    value={profile.isSmoker ? 'yes' : 'no'}
                    onValueChange={(value) => setProfile({ ...profile, isSmoker: value === 'yes' })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="no">🚭 Non-smoker</SelectItem>
                      <SelectItem value="yes">🚬 Smoker</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Different Religion Acceptance</label>
                  <Select
                    disabled={!editing}
                    value={profile.acceptDifferentReligion ? 'yes' : 'no'}
                    onValueChange={(value) => setProfile({ ...profile, acceptDifferentReligion: value === 'yes' })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="yes">🤝 Accept different religions</SelectItem>
                      <SelectItem value="no">🙏 Prefer same religion</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">About Me</label>
                <Textarea
                  disabled={!editing}
                  value={profile.about || ''}
                  onChange={(e) => setProfile({ ...profile, about: e.target.value })}
                  placeholder="Tell us about yourself..."
                  className="h-24"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Photos Gallery */}
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Photo Gallery</span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => photosInputRef.current?.click()}
                disabled={uploadingPhotos || profile.photos.length >= 5}
              >
                {uploadingPhotos ? (
                  <>
                    <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mr-2" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Photos ({profile.photos.length}/5)
                  </>
                )}
              </Button>
              <input
                ref={photosInputRef}
                type="file"
                accept="image/*"
                multiple
                className="hidden"
                onChange={(e) => {
                  const files = e.target.files;
                  if (files && files.length > 0) {
                    const remainingSlots = 5 - profile.photos.length;
                    if (files.length > remainingSlots) {
                      setError(`You can only upload ${remainingSlots} more photos`);
                      return;
                    }
                    handlePhotosUpload(files);
                  }
                }}
              />
            </CardTitle>
            <CardDescription>Upload up to 5 photos to showcase yourself (current: {profile.photos.length}/5)</CardDescription>
          </CardHeader>
          <CardContent>
            {profile.photos.length === 0 ? (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <Camera className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No photos yet</h3>
                <p className="text-gray-500 mb-4">Upload some photos to make your profile more attractive!</p>
                <Button
                  onClick={() => photosInputRef.current?.click()}
                  disabled={uploadingPhotos}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Photos
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {profile.photos.map((photo, index) => (
                  <div key={photo.id} className="relative group">
                    <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                      <img
                        src={photo.url}
                        alt={`Photo ${index + 1}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          console.error('Failed to load image:', photo.url);
                          e.currentTarget.src = '/placeholder-image.png'; // fallback image
                        }}
                        onLoad={() => {
                          console.log('Image loaded successfully:', photo.url);
                        }}
                        loading="lazy"
                        crossOrigin="anonymous"
                      />
                    </div>
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 rounded-lg flex items-center justify-center">
                      <Button
                        size="sm"
                        variant="destructive"
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => {
                          // Note: You might want to implement photo deletion API
                          setProfile({
                            ...profile,
                            photos: profile.photos.filter(p => p.id !== photo.id)
                          });
                        }}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Account Status */}
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle>Account Status</CardTitle>
            <CardDescription>Your account information and status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-[#F2E7DB]/30 rounded-lg">
                <h4 className="font-medium mb-2">Profile Status</h4>
                <Badge variant={profile.status === 'active' ? 'default' : 'secondary'} className="mb-2">
                  {profile.status}
                </Badge>
                <p className="text-sm text-gray-600">
                  Your account is {profile.status}
                </p>
              </div>

              <div className="text-center p-4 bg-[#F2E7DB]/30 rounded-lg">
                <h4 className="font-medium mb-2">Personality Test</h4>
                <Badge variant={profile.psychTestCompleted ? 'default' : 'destructive'} className="mb-2">
                  {profile.psychTestCompleted ? 'Completed' : 'Not Completed'}
                </Badge>
                <p className="text-sm text-gray-600">
                  {profile.psychTestCompleted
                    ? 'You can now find matches!'
                    : 'Complete the test to find matches'
                  }
                </p>
              </div>

              <div className="text-center p-4 bg-[#F2E7DB]/30 rounded-lg">
                <h4 className="font-medium mb-2">Photos</h4>
                <Badge variant={profile.photos.length > 0 ? 'default' : 'secondary'} className="mb-2">
                  {profile.photos.length}/5 Photos
                </Badge>
                <p className="text-sm text-gray-600">
                  {profile.photos.length === 0
                    ? 'Add photos to attract matches'
                    : `${5 - profile.photos.length} more photos can be added`
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
