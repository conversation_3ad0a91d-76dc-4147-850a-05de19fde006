"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { User, Edit, Save } from "lucide-react";
import { useState } from "react";

export default function ProfilePage() {
  const [editing, setEditing] = useState(false);
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Profile</h1>
          <p className="text-muted-foreground">Manage your personal information and preferences.</p>
        </div>
        <Button 
          className={editing ? "bg-green-600 hover:bg-green-700" : "bg-[#D0544D] hover:bg-[#D0544D]/90"}
          onClick={() => setEditing(!editing)}
        >
          {editing ? (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </>
          ) : (
            <>
              <Edit className="mr-2 h-4 w-4" />
              Edit Profile
            </>
          )}
        </Button>
      </div>
      
      <div className="grid gap-6 md:grid-cols-3">
        {/* Profile Summary */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>Profile Summary</CardTitle>
            <CardDescription>Your basic information</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center text-center">
            <div className="w-32 h-32 rounded-full bg-[#D0544D]/20 flex items-center justify-center mb-4">
              <User className="h-16 w-16 text-[#D0544D]" />
            </div>
            <h3 className="text-xl font-bold">User Name</h3>
            <p className="text-sm text-muted-foreground"><EMAIL></p>
            <div className="mt-4 w-full">
              <div className="flex justify-between text-sm mb-1">
                <span>Profile Completion</span>
                <span>80%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-[#D0544D] h-2 rounded-full" style={{ width: "80%" }}></div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Personal Information */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
            <CardDescription>Update your personal details</CardDescription>
          </CardHeader>
          <CardContent>
            <form className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">First Name</label>
                  <input 
                    type="text" 
                    disabled={!editing}
                    defaultValue="User" 
                    className="w-full p-2 border rounded-md disabled:bg-gray-100"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Last Name</label>
                  <input 
                    type="text" 
                    disabled={!editing}
                    defaultValue="Name" 
                    className="w-full p-2 border rounded-md disabled:bg-gray-100"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Email</label>
                <input 
                  type="email" 
                  disabled={!editing}
                  defaultValue="<EMAIL>" 
                  className="w-full p-2 border rounded-md disabled:bg-gray-100"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Phone</label>
                <input 
                  type="tel" 
                  disabled={!editing}
                  defaultValue="+62 812 3456 7890" 
                  className="w-full p-2 border rounded-md disabled:bg-gray-100"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Bio</label>
                <textarea 
                  disabled={!editing}
                  defaultValue="This is a placeholder bio. In a real app, this would contain your personal description." 
                  className="w-full p-2 border rounded-md h-24 disabled:bg-gray-100"
                />
              </div>
            </form>
          </CardContent>
        </Card>
        
        {/* Personality Test Results */}
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle>Personality Test Results</CardTitle>
            <CardDescription>Your psychological profile based on the test</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {["Openness", "Conscientiousness", "Extraversion", "Agreeableness", "Neuroticism"].map((trait, i) => (
                <div key={i} className="bg-[#F2E7DB]/50 p-4 rounded-lg text-center">
                  <h4 className="font-medium mb-2">{trait}</h4>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div 
                      className="bg-[#D0544D] h-2 rounded-full" 
                      style={{ width: `${65 + (i * 5)}%` }}
                    ></div>
                  </div>
                  <p className="text-sm">{65 + (i * 5)}%</p>
                </div>
              ))}
            </div>
            <div className="mt-6 p-4 bg-[#F2E7DB]/30 rounded-lg">
              <h4 className="font-medium mb-2">Personality Summary</h4>
              <p className="text-sm">
                This is a placeholder for your personality test results summary. In the real app, 
                this would contain a detailed analysis of your psychological profile based on the 
                Big Five Inventory and Self-Disclosure assessments.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
