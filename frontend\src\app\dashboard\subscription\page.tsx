import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Check, CreditCard } from "lucide-react";

export default function SubscriptionPage() {
  return (
    <div className="space-y-6 w-full">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Subscription</h1>
        <p className="text-muted-foreground">Manage your subscription plan and billing.</p>
      </div>
      
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Current Plan</CardTitle>
          <CardDescription>Your active subscription details</CardDescription>
        </CardHeader>
        <CardContent className="w-full">
          <div className="bg-[#F2E7DB]/50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold text-[#D0544D]">Premium Plan</h3>
                <p className="text-sm text-muted-foreground">Active until August 5, 2025</p>
              </div>
              <div className="bg-[#D0544D]/10 text-[#D0544D] font-medium px-3 py-1 rounded-full text-sm">
                Active
              </div>
            </div>
            <div className="mt-4 border-t border-gray-200 pt-4">
              <p className="text-sm">Your next billing date is August 5, 2025</p>
              <Button variant="outline" className="mt-4">
                <CreditCard className="mr-2 h-4 w-4" />
                Update Payment Method
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <div className="w-full">
        <h2 className="text-2xl font-bold mb-4">Available Plans</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
          {/* Basic Plan */}
          <Card>
            <CardHeader>
              <CardTitle>Basic</CardTitle>
              <CardDescription>For casual users</CardDescription>
              <div className="mt-2">
                <span className="text-3xl font-bold">Free</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <Check className="h-4 w-4 mr-2 text-green-500" />
                  <span className="text-sm">Limited matches per day</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-4 w-4 mr-2 text-green-500" />
                  <span className="text-sm">Basic personality insights</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-4 w-4 mr-2 text-green-500" />
                  <span className="text-sm">Standard support</span>
                </li>
              </ul>
              <Button className="w-full mt-6 bg-gray-200 text-gray-800 hover:bg-gray-300">
                Current Plan
              </Button>
            </CardContent>
          </Card>
          
          {/* Premium Plan */}
          <Card className="border-[#D0544D] shadow-lg">
            <div className="bg-[#D0544D] text-white text-center py-1 text-sm font-medium">
              POPULAR
            </div>
            <CardHeader>
              <CardTitle>Premium</CardTitle>
              <CardDescription>For serious relationship seekers</CardDescription>
              <div className="mt-2">
                <span className="text-3xl font-bold">Rp 99.000</span>
                <span className="text-muted-foreground">/month</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <Check className="h-4 w-4 mr-2 text-green-500" />
                  <span className="text-sm">Unlimited matches</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-4 w-4 mr-2 text-green-500" />
                  <span className="text-sm">Advanced compatibility analytics</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-4 w-4 mr-2 text-green-500" />
                  <span className="text-sm">Priority messaging</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-4 w-4 mr-2 text-green-500" />
                  <span className="text-sm">Priority support</span>
                </li>
              </ul>
              <Button className="w-full mt-6 bg-[#D0544D] hover:bg-[#D0544D]/90">
                Current Plan
              </Button>
            </CardContent>
          </Card>
          
          {/* VIP Plan */}
          <Card>
            <CardHeader>
              <CardTitle>VIP</CardTitle>
              <CardDescription>For the ultimate experience</CardDescription>
              <div className="mt-2">
                <span className="text-3xl font-bold">Rp 199.000</span>
                <span className="text-muted-foreground">/month</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <Check className="h-4 w-4 mr-2 text-green-500" />
                  <span className="text-sm">All Premium features</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-4 w-4 mr-2 text-green-500" />
                  <span className="text-sm">Personal matchmaking assistance</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-4 w-4 mr-2 text-green-500" />
                  <span className="text-sm">Relationship coaching sessions</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-4 w-4 mr-2 text-green-500" />
                  <span className="text-sm">24/7 dedicated support</span>
                </li>
              </ul>
              <Button className="w-full mt-6 bg-gray-800 hover:bg-gray-700">
                Upgrade
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>Your recent transactions</CardDescription>
        </CardHeader>
        <CardContent className="w-full">
          <div className="overflow-x-auto w-full">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">Date</th>
                  <th className="text-left py-3 px-4">Description</th>
                  <th className="text-left py-3 px-4">Amount</th>
                  <th className="text-left py-3 px-4">Status</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b">
                  <td className="py-3 px-4">Jul 5, 2025</td>
                  <td className="py-3 px-4">Premium Plan - Monthly</td>
                  <td className="py-3 px-4">Rp 99.000</td>
                  <td className="py-3 px-4">
                    <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">
                      Paid
                    </span>
                  </td>
                </tr>
                <tr className="border-b">
                  <td className="py-3 px-4">Jun 5, 2025</td>
                  <td className="py-3 px-4">Premium Plan - Monthly</td>
                  <td className="py-3 px-4">Rp 99.000</td>
                  <td className="py-3 px-4">
                    <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">
                      Paid
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="py-3 px-4">May 5, 2025</td>
                  <td className="py-3 px-4">Premium Plan - Monthly</td>
                  <td className="py-3 px-4">Rp 99.000</td>
                  <td className="py-3 px-4">
                    <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">
                      Paid
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
