'use client';

import { useState, useEffect } from 'react';
import Cookies from 'js-cookie';
import { useRouter } from 'next/navigation';

// Interfaces
interface Question {
  id: string;
  text: string;
}

interface TestData {
  bfi: Question[];
  selfDisclosure: Question[];
}

// Helper component for answer buttons
const AnswerButton = ({ value, label, selectedValue, onClick }: { value: number; label: string; selectedValue: number; onClick: (value: number) => void; }) => (
  <button
    onClick={() => onClick(value)}
    className={`w-full text-left p-4 border rounded-lg transition-all duration-200 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-[#D0544D]/50 ${
      selectedValue === value
        ? 'bg-[#D0544D] text-white border-[#D0544D] shadow-lg'
        : 'bg-white/80 border-gray-300 hover:bg-white'
    }`}>
    <span className={`font-semibold mr-3 px-2 py-1 rounded-md ${selectedValue === value ? 'bg-white/20' : 'bg-gray-200'}`}>{value}</span>
    {label}
  </button>
);

export default function PersonalityTestForm() {
  // State management
  const router = useRouter();
  const [testData, setTestData] = useState<TestData | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [bfiAnswers, setBfiAnswers] = useState<Record<string, number>>({});
  const [selfDisclosureAnswers, setSelfDisclosureAnswers] = useState<Record<string, number>>({});
  const [error, setError] = useState<string | null>(null);
  const [animate, setAnimate] = useState(true);

  // Data fetching
  useEffect(() => {
    const fetchQuestions = async () => {
      setLoading(true);
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }
      try {
        const response = await fetch('https://api.pairsona.id/personality/questions', {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        if (!response.ok) {
            if (response.status === 401) {
                localStorage.removeItem('pairsona_token');
                Cookies.remove('pairsona_token');
                router.replace('/login');
            }
            throw new Error('Failed to fetch questions');
        }
        const rawData = await response.json();
        const bfiQuestions = rawData.bfi?.dimensions?.flatMap((dim: any) => dim.questions) || [];
        const selfDisclosureQuestions = rawData.selfDisclosure?.categories?.flatMap((cat: any) => cat.questions) || [];
        setTestData({
            bfi: bfiQuestions,
            selfDisclosure: selfDisclosureQuestions
        });
      } catch (err) {
        setError('Could not load the test. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    fetchQuestions();
  }, [router]);

  // Event Handlers
  const handleAnswer = (questionId: string, value: number) => {
    const isBfi = currentStep < (testData?.bfi.length || 0);
    const setter = isBfi ? setBfiAnswers : setSelfDisclosureAnswers;
    setter(prev => ({ ...prev, [questionId]: value }));
    // Auto-advance to next question
    setTimeout(() => nextStep(), 200);
  };

  const triggerAnimation = () => {
    setAnimate(false);
    setTimeout(() => setAnimate(true), 50);
  };

  const nextStep = () => {
    if (!testData) return;
    const totalQuestions = testData.bfi.length + testData.selfDisclosure.length;
    if (currentStep < totalQuestions - 1) {
      setCurrentStep(prev => prev + 1);
      triggerAnimation();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
      triggerAnimation();
    }
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    setError(null);
    const token = localStorage.getItem('pairsona_token');
    if (!token) {
        router.replace('/login');
        return;
    }
    try {
      const response = await fetch('https://api.pairsona.id/personality/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
        body: JSON.stringify({ bfiAnswers, selfDisclosureAnswers }),
      });
      if (!response.ok) throw new Error('Failed to submit answers');
      router.replace('/dashboard');
    } catch (err) {
      setError('Failed to submit the test. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  // Render Logic
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#F2E7DB]">
        <div className="w-16 h-16 border-4 border-dashed rounded-full animate-spin border-[#D0544D]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen text-center text-[#D0544D] bg-[#F2E7DB]">
        <p>{error}</p>
      </div>
    );
  }

  if (!testData) return null;

  const allQuestions = [...testData.bfi, ...testData.selfDisclosure];
  const totalQuestions = allQuestions.length;
  const currentQuestion = allQuestions[currentStep];
  const progress = ((currentStep + 1) / totalQuestions) * 100;
  const isBfi = currentStep < testData.bfi.length;
  const currentAnswer = isBfi ? bfiAnswers[currentQuestion.id] : selfDisclosureAnswers[currentQuestion.id];
  const answerLabels = ['Sangat Tidak Setuju', 'Tidak Setuju', 'Netral', 'Setuju', 'Sangat Setuju'];

  return (
    <div className="min-h-screen bg-[#F2E7DB] flex items-center justify-center p-4">
      <div className="w-full max-w-2xl bg-white rounded-2xl shadow-2xl p-8 space-y-8 transform transition-all">
        {/* Progress Bar */}
        <div>
          <div className="flex justify-between mb-1 text-sm font-medium text-gray-500">
            <span>Progress</span>
            <span>{currentStep + 1} / {totalQuestions}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div className="bg-[#D0544D] h-2.5 rounded-full transition-all duration-500 ease-out" style={{ width: `${progress}%` }}></div>
          </div>
        </div>

        {/* Question Area */}
        <div className={`text-center space-y-4 ${animate ? 'fade-in' : 'opacity-0'}`}>
          <p className="text-sm font-semibold text-[#D0544D] uppercase tracking-wider">{isBfi ? 'Big Five Inventory' : 'Self-Disclosure'}</p>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-800">{currentQuestion.text}</h1>
        </div>

        {/* Answer Area */}
        <div className={`space-y-3 ${animate ? 'fade-in' : 'opacity-0'}`}>
          {answerLabels.map((label, index) => (
            <AnswerButton 
              key={index} 
              value={index + 1} 
              label={label} 
              selectedValue={currentAnswer} 
              onClick={(value) => handleAnswer(currentQuestion.id, value)} 
            />
          ))}
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between pt-4">
          <button 
            onClick={prevStep} 
            disabled={currentStep === 0}
            className="px-6 py-2 text-gray-600 font-semibold rounded-lg transition hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
            Back
          </button>
          {currentStep === totalQuestions - 1 ? (
            <button 
              onClick={handleSubmit} 
              disabled={!currentAnswer || submitting}
              className="px-8 py-3 bg-[#D0544D] text-white font-bold rounded-lg shadow-md hover:bg-[#D0544D]/90 transition disabled:opacity-50 disabled:cursor-wait">
              {submitting ? 'Submitting...' : 'Complete Test'}
            </button>
          ) : (
             <button 
              onClick={nextStep} 
              disabled={!currentAnswer}
              className="px-8 py-3 bg-[#D0544D] text-white font-bold rounded-lg shadow-md hover:bg-[#D0544D]/90 transition disabled:opacity-50">
              Next
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
