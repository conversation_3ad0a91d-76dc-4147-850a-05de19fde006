'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Heart, MessageCircle, X, Check } from "lucide-react";

interface MatchDetails {
  extraversion: number;
  agreeableness: number;
  conscientiousness: number;
  negativeEmotionality: number;
  openMindedness: number;
  selfDisclosure: number;
}

interface User {
  id: string;
  name: string;
  email?: string;
  image?: string;
  religion?: string;
  gender?: string;
  age?: number;
  occupation?: string;
  isSmoker?: boolean;
  acceptDifferentReligion?: boolean;
  about?: string;
}

interface Match {
  id: string;   // Match ID from backend
  user: User;
  matchScore: number;
  matchDetails: MatchDetails;
  interpretation: string;
  status?: 'pending' | 'invited' | 'accepted' | 'rejected';
}

export default function MatchesPage() {
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    fetchMatches();
  }, []);

  const fetchMatches = async () => {
    try {
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/match`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('pairsona_token');
          router.replace('/login');
          return;
        }
        throw new Error('Failed to fetch matches');
      }

      const data = await response.json();
      console.log('Matches data from API:', data); // Debug log
      setMatches(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleMatchAction = async (matchId: string, action: 'invite' | 'accept' | 'reject') => {
    try {
      console.log(`Attempting to ${action} match with ID:`, matchId); // Debug log
      setActionLoading(matchId);
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/match/${matchId}/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to ${action} match`);
      }

      // Update local state
      setMatches(prev => prev.map(match =>
        match.id === matchId
          ? { ...match, status: action === 'invite' ? 'invited' : action === 'accept' ? 'accepted' : 'rejected' }
          : match
      ));

      // Refresh matches to get updated data
      await fetchMatches();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setActionLoading(null);
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getScoreColor = (score: number) => {
    const percentage = score * 100; // Convert 0-1 scale to percentage
    if (percentage >= 80) return 'text-green-600 bg-green-50';
    if (percentage >= 60) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const formatMatchScore = (score: number) => {
    return Math.round(score * 100); // Convert 0-1 scale to percentage
  };

  const getReligionIcon = (religion: string) => {
    const religionMap: { [key: string]: string } = {
      'islam': '☪️',
      'kristen': '✝️',
      'katolik': '✝️',
      'hindu': '🕉️',
      'buddha': '☸️',
      'konghucu': '☯️'
    };
    return religionMap[religion.toLowerCase()] || '🙏';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Matches</h1>
          <p className="text-muted-foreground">Find your perfect match based on psychology.</p>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={`loading-${i}`} className="overflow-hidden animate-pulse">
              <div className="h-32 bg-gray-200"></div>
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Matches</h1>
          <p className="text-muted-foreground">Find your perfect match based on psychology.</p>
        </div>
        <Card className="p-6">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchMatches}>Try Again</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Matches</h1>
        <p className="text-muted-foreground">Find your perfect match based on psychology.</p>
      </div>

      {matches.length === 0 ? (
        <Card className="p-6">
          <div className="text-center">
            <Heart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No matches yet</h3>
            <p className="text-gray-500">Complete your personality test to find your perfect matches!</p>
          </div>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {matches.map((match, index) => (
            <Card key={match.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="h-32 bg-[#F2E7DB] flex items-center justify-center relative">
                <Avatar className="w-16 h-16">
                  <AvatarImage src={match.user.image} alt={match.user.name} />
                  <AvatarFallback className="bg-[#D0544D]/20 text-[#D0544D] text-lg font-bold">
                    {getInitials(match.user.name)}
                  </AvatarFallback>
                </Avatar>
                <Badge
                  className={`absolute top-4 right-4 ${getScoreColor(match.matchScore)}`}
                  variant="secondary"
                >
                  {formatMatchScore(match.matchScore)}%
                </Badge>
              </div>

              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between">
                  <span>{match.user.name}</span>
                  {match.status && (
                    <Badge variant={match.status === 'accepted' ? 'default' : 'secondary'}>
                      {match.status}
                    </Badge>
                  )}
                </CardTitle>
                <CardDescription>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <span>{match.user.age} tahun</span>
                      <span>•</span>
                      <span>{match.user.occupation}</span>
                      <span>•</span>
                      <span className="flex items-center gap-1">
                        {getReligionIcon(match.user.religion || '')}
                        {match.user.religion}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>{match.user.isSmoker ? '🚬 Smoker' : '🚭 Non-smoker'}</span>
                      <span>•</span>
                      <span>{match.user.acceptDifferentReligion ? '🤝 Open to different religions' : '🙏 Same religion preferred'}</span>
                    </div>
                    {match.user.about && (
                      <p className="text-sm text-gray-600 italic">"{match.user.about}"</p>
                    )}
                    <p className="text-sm font-medium text-[#D0544D]">{match.interpretation}</p>
                  </div>
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex gap-2">
                    {!match.status && (
                      <Button
                        onClick={() => handleMatchAction(match.id, 'invite')}
                        disabled={actionLoading === match.id}
                        className="flex-1 bg-[#D0544D] hover:bg-[#D0544D]/90"
                        size="sm"
                      >
                        <Heart className="w-4 h-4 mr-1" />
                        {actionLoading === match.id ? 'Inviting...' : 'Invite'}
                      </Button>
                    )}

                    {match.status === 'invited' && (
                      <>
                        <Button
                          onClick={() => handleMatchAction(match.id, 'accept')}
                          disabled={actionLoading === match.id}
                          className="flex-1 bg-green-600 hover:bg-green-700"
                          size="sm"
                        >
                          <Check className="w-4 h-4 mr-1" />
                          Accept
                        </Button>
                        <Button
                          onClick={() => handleMatchAction(match.id, 'reject')}
                          disabled={actionLoading === match.id}
                          variant="outline"
                          className="flex-1"
                          size="sm"
                        >
                          <X className="w-4 h-4 mr-1" />
                          Reject
                        </Button>
                      </>
                    )}

                    {match.status === 'accepted' && (
                      <Button
                        className="flex-1 bg-blue-600 hover:bg-blue-700"
                        size="sm"
                      >
                        <MessageCircle className="w-4 h-4 mr-1" />
                        Chat
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
