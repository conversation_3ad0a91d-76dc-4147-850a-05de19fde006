'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Heart, MessageCircle, X, Check } from "lucide-react";

interface MatchDetails {
  extraversion: number;
  agreeableness: number;
  conscientiousness: number;
  negativeEmotionality: number;
  openMindedness: number;
  selfDisclosure: number;
}

interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
}

interface Match {
  id: string;
  user: User;
  matchScore: number;
  matchDetails: MatchDetails;
  interpretation: string;
  status?: 'pending' | 'invited' | 'accepted' | 'rejected';
}

export default function MatchesPage() {
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    fetchMatches();
  }, []);

  const fetchMatches = async () => {
    try {
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/match`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('pairsona_token');
          router.replace('/login');
          return;
        }
        throw new Error('Failed to fetch matches');
      }

      const data = await response.json();
      setMatches(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleMatchAction = async (matchId: string, action: 'invite' | 'accept' | 'reject') => {
    try {
      setActionLoading(matchId);
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/match/${matchId}/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to ${action} match`);
      }

      // Update local state
      setMatches(prev => prev.map(match =>
        match.id === matchId
          ? { ...match, status: action === 'invite' ? 'invited' : action === 'accept' ? 'accepted' : 'rejected' }
          : match
      ));

      // Refresh matches to get updated data
      await fetchMatches();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setActionLoading(null);
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50';
    if (score >= 60) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Matches</h1>
          <p className="text-muted-foreground">Find your perfect match based on psychology.</p>
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="overflow-hidden animate-pulse">
              <div className="h-48 bg-gray-200"></div>
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Matches</h1>
          <p className="text-muted-foreground">Find your perfect match based on psychology.</p>
        </div>
        <Card className="p-6">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchMatches}>Try Again</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Matches</h1>
        <p className="text-muted-foreground">Find your perfect match based on psychology.</p>
      </div>

      {matches.length === 0 ? (
        <Card className="p-6">
          <div className="text-center">
            <Heart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No matches yet</h3>
            <p className="text-gray-500">Complete your personality test to find your perfect matches!</p>
          </div>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {matches.map((match) => (
            <Card key={match.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="h-48 bg-[#F2E7DB] flex items-center justify-center relative">
                <Avatar className="w-24 h-24">
                  <AvatarImage src={match.user.image} alt={match.user.name} />
                  <AvatarFallback className="bg-[#D0544D]/20 text-[#D0544D] text-xl font-bold">
                    {getInitials(match.user.name)}
                  </AvatarFallback>
                </Avatar>
                <Badge
                  className={`absolute top-4 right-4 ${getScoreColor(match.matchScore)}`}
                  variant="secondary"
                >
                  {Math.round(match.matchScore)}%
                </Badge>
              </div>

              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{match.user.name}</span>
                  {match.status && (
                    <Badge variant={match.status === 'accepted' ? 'default' : 'secondary'}>
                      {match.status}
                    </Badge>
                  )}
                </CardTitle>
                <CardDescription>{match.interpretation}</CardDescription>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  <div className="text-sm">
                    <p className="font-medium mb-2">Compatibility Details:</p>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>Extraversion: {match.matchDetails.extraversion.toFixed(1)}</div>
                      <div>Agreeableness: {match.matchDetails.agreeableness.toFixed(1)}</div>
                      <div>Conscientiousness: {match.matchDetails.conscientiousness.toFixed(1)}</div>
                      <div>Emotional Stability: {(5 - match.matchDetails.negativeEmotionality).toFixed(1)}</div>
                      <div>Openness: {match.matchDetails.openMindedness.toFixed(1)}</div>
                      <div>Self Disclosure: {match.matchDetails.selfDisclosure.toFixed(1)}</div>
                    </div>
                  </div>

                  <div className="flex gap-2 pt-2">
                    {!match.status && (
                      <Button
                        onClick={() => handleMatchAction(match.id, 'invite')}
                        disabled={actionLoading === match.id}
                        className="flex-1 bg-[#D0544D] hover:bg-[#D0544D]/90"
                        size="sm"
                      >
                        <Heart className="w-4 h-4 mr-1" />
                        {actionLoading === match.id ? 'Inviting...' : 'Invite'}
                      </Button>
                    )}

                    {match.status === 'invited' && (
                      <>
                        <Button
                          onClick={() => handleMatchAction(match.id, 'accept')}
                          disabled={actionLoading === match.id}
                          className="flex-1 bg-green-600 hover:bg-green-700"
                          size="sm"
                        >
                          <Check className="w-4 h-4 mr-1" />
                          Accept
                        </Button>
                        <Button
                          onClick={() => handleMatchAction(match.id, 'reject')}
                          disabled={actionLoading === match.id}
                          variant="outline"
                          className="flex-1"
                          size="sm"
                        >
                          <X className="w-4 h-4 mr-1" />
                          Reject
                        </Button>
                      </>
                    )}

                    {match.status === 'accepted' && (
                      <Button
                        className="flex-1 bg-blue-600 hover:bg-blue-700"
                        size="sm"
                      >
                        <MessageCircle className="w-4 h-4 mr-1" />
                        Chat
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
