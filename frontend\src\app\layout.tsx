import type { <PERSON>ada<PERSON> } from "next";
import { Montser<PERSON> } from "next/font/google";
import "@/styles/globals.css";

const montserrat = Montserrat({
  subsets: ["latin"],
  variable: "--font-montserrat",
});

export const metadata: Metadata = {
  title: "Pairsona - Find Your Perfect Match",
  description: "Find your perfect match based on psychology with <PERSON>irs<PERSON>",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${montserrat.variable} font-montserrat antialiased bg-eggshell`}>
        {children}
      </body>
    </html>
  );
}
