import { <PERSON>, CardContent, <PERSON>H<PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { MessageSquare, Send } from "lucide-react";

export default function ChatPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Chat</h1>
        <p className="text-muted-foreground">Connect with your matches.</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Chat list sidebar */}
        <Card className="md:col-span-1 h-[calc(100vh-200px)]">
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquare className="h-5 w-5 mr-2 text-[#D0544D]" />
              Conversations
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="divide-y">
              {["Match #1", "Match #2", "Match #3"].map((match, i) => (
                <div 
                  key={i} 
                  className={`p-4 cursor-pointer hover:bg-[#F2E7DB]/50 transition-colors ${i === 0 ? 'bg-[#F2E7DB]/30' : ''}`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-full bg-[#D0544D]/20 flex items-center justify-center">
                      <span className="text-[#D0544D] font-bold">
                        {String.fromCharCode(65 + i)}
                      </span>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">{match}</p>
                      <p className="text-xs text-muted-foreground truncate">Last message preview...</p>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {i === 0 ? 'Now' : `${i + 1}h ago`}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        
        {/* Chat area */}
        <Card className="md:col-span-2 h-[calc(100vh-200px)] flex flex-col">
          <CardHeader className="border-b">
            <CardTitle className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-[#D0544D]/20 flex items-center justify-center mr-2">
                <span className="text-[#D0544D] font-bold">A</span>
              </div>
              Match #1
            </CardTitle>
          </CardHeader>
          <CardContent className="flex-1 overflow-auto p-4">
            <div className="space-y-4">
              {/* Sample messages */}
              <div className="flex justify-start">
                <div className="bg-[#F2E7DB] rounded-lg p-3 max-w-[80%]">
                  <p className="text-sm">Hello! Nice to meet you.</p>
                  <p className="text-xs text-muted-foreground mt-1">10:30 AM</p>
                </div>
              </div>
              <div className="flex justify-end">
                <div className="bg-[#D0544D] text-white rounded-lg p-3 max-w-[80%]">
                  <p className="text-sm">Hi there! Nice to meet you too. How are you doing today?</p>
                  <p className="text-xs text-white/70 mt-1">10:32 AM</p>
                </div>
              </div>
              <div className="flex justify-start">
                <div className="bg-[#F2E7DB] rounded-lg p-3 max-w-[80%]">
                  <p className="text-sm">I'm doing great! I saw we have a high compatibility score. What do you like to do for fun?</p>
                  <p className="text-xs text-muted-foreground mt-1">10:35 AM</p>
                </div>
              </div>
            </div>
          </CardContent>
          <div className="p-4 border-t">
            <div className="flex space-x-2">
              <input 
                type="text" 
                placeholder="Type a message..." 
                className="flex-1 px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#D0544D]"
              />
              <button className="p-2 bg-[#D0544D] text-white rounded-md hover:bg-[#D0544D]/90 transition-colors">
                <Send className="h-5 w-5" />
              </button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
