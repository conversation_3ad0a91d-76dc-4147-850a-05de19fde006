@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

@theme {
  /* Font */
  --font-montserrat: "Montserrat", sans-serif;
  
  /* Warna utama <PERSON>irsona */
  --color-accent-red: #D0544D;
  --color-eggshell: #F2E7DB;
  
  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-full: 9999px;
}

@layer base {
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .fade-in {
    animation: fadeIn 0.5s ease-in-out forwards;
  }

  body {
    font-family: var(--font-montserrat);
    background-color: var(--color-eggshell);
    color: #333;
  }
}

/* Shadcn UI compatibility */
:root {
  --background: var(--color-eggshell);
  --foreground: #333;
  
  --card: var(--color-eggshell);
  --card-foreground: #333;
  
  --popover: var(--color-eggshell);
  --popover-foreground: #333;
  
  --primary: var(--color-accent-red);
  --primary-foreground: white;
  
  --secondary: #f5f5f5;
  --secondary-foreground: #333;
  
  --muted: #f5f5f5;
  --muted-foreground: #666;
  
  --accent: #f5f5f5;
  --accent-foreground: #333;
  
  --destructive: #ef4444;
  --destructive-foreground: white;

  --border: #e5e5e5;
  --input: #e5e5e5;
  --ring: var(--color-accent-red);
}

.dark {
  --background: #1a1a1a;
  --foreground: #f5f5f5;
  
  --card: #1a1a1a;
  --card-foreground: #f5f5f5;
  
  --popover: #1a1a1a;
  --popover-foreground: #f5f5f5;
  
  --primary: var(--color-accent-red);
  --primary-foreground: white;
  
  --secondary: #2a2a2a;
  --secondary-foreground: #f5f5f5;
  
  --muted: #2a2a2a;
  --muted-foreground: #a3a3a3;
  
  --accent: #2a2a2a;
  --accent-foreground: #f5f5f5;
  
  --destructive: #7f1d1d;
  --destructive-foreground: #f5f5f5;
  
  --border: #2a2a2a;
  --input: #2a2a2a;
  --ring: var(--color-accent-red);
}