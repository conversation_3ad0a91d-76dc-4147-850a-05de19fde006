import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const token = request.cookies.get('pairsona_token')?.value;
  const { pathname } = request.nextUrl;

  const isAuthRoute = pathname.startsWith('/login') || pathname.startsWith('/register') || pathname.startsWith('/verify-otp');

  // If there's no token and the user is trying to access a protected page, redirect to login.
  // The setup page is a special case that handles its own token logic, so we allow it.
  if (!token && !isAuthRoute && !pathname.startsWith('/auth/setup')) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // If there is a token and the user is trying to access a primary auth page (login/register),
  // we redirect them to the setup page. The setup page will then figure out where they should *actually* go.
  if (token && isAuthRoute) {
    return NextResponse.redirect(new URL('/auth/setup', request.url));
  }

  // Otherwise, allow the request to proceed.
  return NextResponse.next();
}

// Configure which paths the middleware runs on
export const config = {
  matcher: [
    // Apply to all routes except api, _next, and static files
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$).*)',
  ],
};
