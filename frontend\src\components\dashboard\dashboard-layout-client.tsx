"use client";

import { AppSidebar } from '@/components/app-sidebar';
import { SidebarProvider } from '@/components/ui/sidebar';

export function DashboardLayoutClient({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <SidebarProvider>
      <div className="flex h-screen bg-[#F2E7DB] w-full">
        <AppSidebar className="hidden md:flex" />
        <div className="flex-1 overflow-auto w-full bg-white">
          <main className="p-4 w-full">
            {children}
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
}
