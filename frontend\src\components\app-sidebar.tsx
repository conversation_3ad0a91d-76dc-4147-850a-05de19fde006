"use client"

import * as React from "react"
import {
  Heart,
  MessageSquare,
  User,
  CreditCard,
  LogOut,
  HelpCircle,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  user: {
    name: "User",
    email: "<EMAIL>",
    avatar: "/avatars/user.jpg",
  },
  navMain: [
    {
      title: "Matches",
      url: "/dashboard/matches",
      icon: Heart,
      isActive: true,
      items: [],
    },
    {
      title: "Chat",
      url: "/dashboard/chat",
      icon: MessageSquare,
      items: [],
    },
    {
      title: "My Profile",
      url: "/dashboard/profile",
      icon: User,
      items: [],
    },
    {
      title: "Subscription",
      url: "/dashboard/subscription",
      icon: CreditCard,
      items: [],
    },
  ],
  navSecondary: [
    {
      title: "Help",
      url: "#",
      icon: HelpCircle,
    },
    {
      title: "Logout",
      url: "/logout",
      icon: LogOut,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar variant="inset" {...props} className="bg-[#F2E7DB] border-r border-gray-200">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="/dashboard" className="flex justify-center">
                <img
                  src="/logo.png"
                  alt="Pairsona Logo"
                  className="h-12 w-auto object-contain"
                />
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  )
}
