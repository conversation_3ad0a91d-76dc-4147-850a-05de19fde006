"use client"

import * as React from "react"
import {
  Heart,
  MessageSquare,
  User,
  CreditCard,
  Command,
  LogOut,
  HelpCircle,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  user: {
    name: "User",
    email: "<EMAIL>",
    avatar: "/avatars/user.jpg",
  },
  navMain: [
    {
      title: "Matches",
      url: "/dashboard/matches",
      icon: Heart,
      isActive: true,
      items: [],
    },
    {
      title: "Chat",
      url: "/dashboard/chat",
      icon: MessageSquare,
      items: [],
    },
    {
      title: "My Profile",
      url: "/dashboard/profile",
      icon: User,
      items: [],
    },
    {
      title: "Subscription",
      url: "/dashboard/subscription",
      icon: CreditCard,
      items: [],
    },
  ],
  navSecondary: [
    {
      title: "Help",
      url: "#",
      icon: HelpCircle,
    },
    {
      title: "Logout",
      url: "/logout",
      icon: LogOut,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar variant="inset" {...props} className="bg-white border-r border-gray-200">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="/dashboard">
                <div className="bg-[#D0544D] text-white flex aspect-square size-8 items-center justify-center rounded-lg">
                  <Command className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">Pairsona</span>
                  <span className="truncate text-xs">Find Your Perfect Match</span>
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  )
}
